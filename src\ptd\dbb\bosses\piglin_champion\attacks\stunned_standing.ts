import { Entity, system } from "@minecraft/server";
import { stopPiglinChampionSounds } from "../soundManager";

/**
 * Total animation time in ticks for stunned standing (phase 2 duration)
 */
const ANIMATION_TIME = 240;

/**
 * Duration of stunned_to_idle animation in ticks (phase 3)
 */
const STUNNED_TO_IDLE_TIME = 90; // 4.5 seconds

/**
 * Total duration of all stun phases in ticks
 */
const TOTAL_STUN_TIME = ANIMATION_TIME + STUNNED_TO_IDLE_TIME;

/**
 * Cooldown time in ticks after the stun completes
 */
const COOLDOWN_TIME = 20;

/**
 * Executes the stunned standing for the Piglin Champion using the new timing system
 * Uses localized runTimeout for stun duration, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeStunnedStanding(piglinChampion: Entity): void {
  const entityId = piglinChampion.id;
  console.warn(`[STUN DEBUG] executeStunnedStanding called for entity ${entityId}`);
  console.warn(`[STUN DEBUG] ANIMATION_TIME: ${ANIMATION_TIME} ticks (${ANIMATION_TIME/20} seconds)`);
  console.warn(`[STUN DEBUG] STUNNED_TO_IDLE_TIME: ${STUNNED_TO_IDLE_TIME} ticks (${STUNNED_TO_IDLE_TIME/20} seconds)`);
  console.warn(`[STUN DEBUG] TOTAL_STUN_TIME: ${TOTAL_STUN_TIME} ticks (${TOTAL_STUN_TIME/20} seconds)`);

  // Apply slowness effect for the entire duration and remove component groups
  piglinChampion.addEffect("minecraft:slowness", TOTAL_STUN_TIME, { amplifier: 250, showParticles: false });
  console.warn(`[STUN DEBUG] Applied slowness effect for ${TOTAL_STUN_TIME} ticks`);

  // Remove default and targeting component groups to prevent looking around
  piglinChampion.triggerEvent("ptd_dbb:remove_targeting");
  console.warn(`[STUN DEBUG] Triggered remove_targeting event`);

  // Restore targeting after the phase 2 duration
  console.warn(`[STUN DEBUG] Setting up restoreTargeting timeout for ${ANIMATION_TIME} ticks`);
  let restoreTargeting = system.runTimeout(() => {
    try {
      console.warn(`[STUN DEBUG] restoreTargeting timeout triggered after ${ANIMATION_TIME} ticks`);
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
      const currentAttack = piglinChampion.getProperty("ptd_dbb:attack") as string;
      console.warn(`[STUN DEBUG] restoreTargeting - isDead: ${isDead}, currentAttack: ${currentAttack}`);

      if (isDead) {
        console.warn(`[STUN DEBUG] Entity is dead, clearing restoreTargeting timeout`);
        system.clearRun(restoreTargeting);
        return;
      }

      // Restore component groups and stop sounds
      console.warn(`[STUN DEBUG] Triggering restore_targeting event`);
      piglinChampion.triggerEvent("ptd_dbb:restore_targeting");
      stopPiglinChampionSounds(piglinChampion);
      system.clearRun(restoreTargeting);
    } catch (error) {
      console.warn(`[STUN DEBUG] Error in restoreTargeting timeout: ${error}`);
      system.clearRun(restoreTargeting);
    }
  }, ANIMATION_TIME);

  // Reset attack after all stun phases complete (phase 2 + phase 3)
  console.warn(`[STUN DEBUG] Setting up resetAttack timeout for ${TOTAL_STUN_TIME} ticks`);
  let resetAttack = system.runTimeout(() => {
    try {
      console.warn(`[STUN DEBUG] resetAttack timeout triggered after ${TOTAL_STUN_TIME} ticks`);
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
      const currentAttack = piglinChampion.getProperty("ptd_dbb:attack") as string;
      console.warn(`[STUN DEBUG] resetAttack - isDead: ${isDead}, currentAttack: ${currentAttack}`);

      if (isDead) {
        console.warn(`[STUN DEBUG] Entity is dead, clearing resetAttack timeout`);
        system.clearRun(resetAttack);
        return;
      }

      // Reset attack to allow normal behavior to resume
      console.warn(`[STUN DEBUG] Triggering reset_attack event - this should set attack to 'none'`);
      piglinChampion.triggerEvent("ptd_dbb:reset_attack");
      system.clearRun(resetAttack);
    } catch (error) {
      console.warn(`[STUN DEBUG] Error in resetAttack timeout: ${error}`);
      system.clearRun(resetAttack);
    }
  }, TOTAL_STUN_TIME);

  // Wait for cooldown, then set cooldown property to false to allow next attack
  console.warn(`[STUN DEBUG] Setting up cooldown timeout for ${TOTAL_STUN_TIME + COOLDOWN_TIME} ticks`);
  let cooldown = system.runTimeout(() => {
    try {
      console.warn(`[STUN DEBUG] cooldown timeout triggered after ${TOTAL_STUN_TIME + COOLDOWN_TIME} ticks`);
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
      const currentAttack = piglinChampion.getProperty("ptd_dbb:attack") as string;
      console.warn(`[STUN DEBUG] cooldown - isDead: ${isDead}, currentAttack: ${currentAttack}`);

      if (isDead) {
        console.warn(`[STUN DEBUG] Entity is dead, clearing cooldown timeout`);
        system.clearRun(cooldown);
        return;
      }

      console.warn(`[STUN DEBUG] Setting cooling_down to false`);
      piglinChampion.setProperty("ptd_dbb:cooling_down", false);
      system.clearRun(cooldown);
    } catch (error) {
      console.warn(`[STUN DEBUG] Error in cooldown timeout: ${error}`);
      system.clearRun(cooldown);
    }
  }, TOTAL_STUN_TIME + COOLDOWN_TIME);

  console.warn(`[STUN DEBUG] executeStunnedStanding setup complete for entity ${entityId}`);
  return;
}
