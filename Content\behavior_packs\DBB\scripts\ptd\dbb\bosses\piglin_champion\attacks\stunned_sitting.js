import { system } from "@minecraft/server";
import { stopPiglinChampionSounds } from "../soundManager";
/**
 * Total animation time in ticks for stunned sitting (phase 2 duration)
 */
const ANIMATION_TIME = 333;
/**
 * Duration of stunned_sitting_to_idle animation in ticks (phase 3)
 */
const STUNNED_TO_IDLE_TIME = 183; // 9.125 seconds
/**
 * Total duration of all stun phases in ticks
 */
const TOTAL_STUN_TIME = ANIMATION_TIME + STUNNED_TO_IDLE_TIME;
/**
 * Cooldown time in ticks after the stun completes
 */
const COOLDOWN_TIME = 20;
/**
 * Executes the stunned sitting for the Piglin Champion using the new timing system
 * Uses localized runTimeout for stun duration, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeStunnedSitting(piglinChampion) {
    // Apply slowness effect for the entire duration and remove component groups
    piglinChampion.addEffect("minecraft:slowness", TOTAL_STUN_TIME, { amplifier: 250, showParticles: false });
    // Remove default and targeting component groups to prevent looking around
    piglinChampion.triggerEvent("ptd_dbb:remove_targeting");
    // Restore targeting after the phase 2 duration
    let restoreTargeting = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(restoreTargeting);
                return;
            }
            // Restore component groups and stop sounds
            piglinChampion.triggerEvent("ptd_dbb:restore_targeting");
            stopPiglinChampionSounds(piglinChampion);
            system.clearRun(restoreTargeting);
        }
        catch (error) {
            system.clearRun(restoreTargeting);
        }
    }, ANIMATION_TIME);
    // Reset attack after all stun phases complete (phase 2 + phase 3)
    let resetAttack = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetAttack);
                return;
            }
            // Reset attack to allow normal behavior to resume
            piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            system.clearRun(resetAttack);
        }
        catch (error) {
            system.clearRun(resetAttack);
        }
    }, TOTAL_STUN_TIME);
    // Wait for cooldown, then set cooldown property to false to allow next attack
    let cooldown = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldown);
                return;
            }
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
            system.clearRun(cooldown);
        }
        catch (error) {
            system.clearRun(cooldown);
        }
    }, TOTAL_STUN_TIME + COOLDOWN_TIME);
    return;
}
