import { system } from "@minecraft/server";
/**
 * Attack timing in ticks - when the damage should be applied during the animation
 */
const ATTACK_TIMING = 55;
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 170;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Executes the upchuck attack for the Piglin Champion using the new timing system
 * Uses localized runTimeout for attack timing, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeUpchuckAttack(piglinChampion) {
    // Apply slowness effect for the entire duration and remove component groups
    piglinChampion.addEffect("minecraft:slowness", ANIMATION_TIME, { amplifier: 250, showParticles: false });
    // Remove default and targeting component groups to prevent looking around
    piglinChampion.triggerEvent("ptd_dbb:remove_targeting");
    // Wait for the attack timing before executing the attack
    let timing = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(timing);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "upchuck") {
                performUpchuckAttack(piglinChampion);
            }
        }
        catch (error) {
            system.clearRun(timing);
        }
    }, ATTACK_TIMING);
    // Reset the attack state to "none" after the animation is complete
    let reset = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            console.warn(`[UPCHUCK DEBUG] Reset timeout triggered - isDead: ${isDead}, currentAttack: ${currentAttack}`);
            if (isDead) {
                console.warn(`[UPCHUCK DEBUG] Entity is dead, clearing reset timeout`);
                system.clearRun(reset);
                return;
            }
            // Only reset if the attack is still "upchuck" - prevents interference with stuns
            if (currentAttack === "upchuck") {
                console.warn(`[UPCHUCK DEBUG] Resetting upchuck attack to none`);
                // Restore component groups for normal behavior
                piglinChampion.triggerEvent("ptd_dbb:restore_targeting");
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
            else {
                console.warn(`[UPCHUCK DEBUG] Attack changed to ${currentAttack}, not resetting upchuck`);
            }
            system.clearRun(reset);
        }
        catch (error) {
            console.warn(`[UPCHUCK DEBUG] Error in reset timeout: ${error}`);
            system.clearRun(reset);
        }
    }, ANIMATION_TIME);
    // Wait for cooldown, then set cooldown property to false to select the next attack
    let cooldown = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            console.warn(`[UPCHUCK DEBUG] Cooldown timeout triggered - isDead: ${isDead}, currentAttack: ${currentAttack}`);
            if (isDead) {
                console.warn(`[UPCHUCK DEBUG] Entity is dead, clearing cooldown timeout`);
                system.clearRun(cooldown);
                return;
            }
            // Only set cooldown to false if we're not in a stun or other special state
            if (currentAttack === "none") {
                console.warn(`[UPCHUCK DEBUG] Setting cooling_down to false`);
                piglinChampion.setProperty("ptd_dbb:cooling_down", false);
            }
            else {
                console.warn(`[UPCHUCK DEBUG] Attack is ${currentAttack}, not setting cooling_down to false`);
            }
            system.clearRun(cooldown);
        }
        catch (error) {
            console.warn(`[UPCHUCK DEBUG] Error in cooldown timeout: ${error}`);
            system.clearRun(cooldown);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
    return;
}
/**
 * Performs the actual upchuck attack damage and effects
 * @param piglinChampion The piglin champion entity
 */
function performUpchuckAttack(piglinChampion) {
    // Apply poison to nearby entities
    const poisonRadius = 6;
    const poisonDuration = 60; // 3 seconds at 20 ticks per second
    const poisonAmplifier = 1; // Poison II
    // Get direction vector directly from the piglin's view direction
    const viewDirection = piglinChampion.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;
    // Calculate position 2 blocks in front of the piglin as the origin for the attack
    const originPos = {
        x: piglinChampion.location.x + (dirX * 2),
        y: piglinChampion.location.y,
        z: piglinChampion.location.z + (dirZ * 2)
    };
    // Spawn poison cloud particles at the origin
    piglinChampion.dimension.spawnParticle("minecraft:mob_portal", originPos);
    // Play a sound effect for the poison attack
    piglinChampion.dimension.playSound("mob.slime.attack", originPos);
    // Start continuous poison application for the remaining duration
    startContinuousPoisonEffect(piglinChampion, originPos, poisonRadius, poisonDuration, poisonAmplifier);
}
/**
 * Continuously applies poison effect to entities within a radius
 * Continues for the remaining duration of the attack (170 - 55 = 115 ticks)
 *
 * @param piglinChampion The piglin champion entity
 * @param originPos The origin position for the poison effect
 * @param radius The radius of the poison effect
 * @param duration The duration of each poison effect application
 * @param amplifier The amplifier of the poison effect
 */
function startContinuousPoisonEffect(piglinChampion, originPos, radius, duration, amplifier) {
    // Duration for continuous poison effect (from timing until animation end)
    const continuousDuration = ANIMATION_TIME - ATTACK_TIMING; // 115 ticks
    let ticksElapsed = 0;
    // Apply poison effect continuously
    const intervalId = system.runInterval(() => {
        try {
            // Check if the entity is still valid and the attack is still "upchuck"
            const attack = piglinChampion.getProperty("ptd_dbb:attack");
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (attack !== "upchuck" || isDead || ticksElapsed >= continuousDuration) {
                system.clearRun(intervalId);
                return;
            }
            // Apply poison to entities within the radius
            piglinChampion.dimension.getEntities({
                location: originPos,
                maxDistance: radius,
                excludeFamilies: ["piglin_champion", "piglin", "rock", "inanimate"]
            }).forEach(entity => {
                // Apply poison only to entities that are not XP orbs or items
                if (entity.typeId !== "minecraft:xp_orb" && entity.typeId !== "minecraft:item") {
                    // Apply poison effect
                    entity.addEffect("minecraft:fatal_poison", duration, { amplifier: amplifier, showParticles: true });
                }
            });
            // Spawn poison cloud particles at the origin
            piglinChampion.dimension.spawnParticle("minecraft:mob_portal", originPos);
            ticksElapsed++;
        }
        catch (e) {
            // Entity might have been removed
            system.clearRun(intervalId);
        }
    }, 1); // Apply poison effect every tick
}
